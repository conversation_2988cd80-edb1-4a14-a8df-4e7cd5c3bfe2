# Re-run this cell
# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
crimes = pd.read_csv("crimes.csv", dtype={"TIME OCC": str})
crimes.head()

1. Which hour has the highest frequency of crimes? Store as an integer variable called peak_crime_hour.

crimes["HOUR_OCC"] = crimes["TIME OCC"].str[:2].astype(int)
peak_crime_hour = crimes["HOUR_OCC"].mode()[0]
print(peak_crime_hour)

sns.histplot(crimes["HOUR_OCC"], bins=24, kde=False)
plt.title("Frequency of Crimes by Hour")
plt.xlabel("Hour")
plt.ylabel("Frequency")
plt.show()

crimes["NIGHT_CRIME"] = crimes["HOUR_OCC"].between(22, 23) | crimes["HOUR_OCC"].between(0, 5)
peak_night_crime_location = crimes[crimes["NIGHT_CRIME"] == True]["AREA NAME"].mode()[0]
print(peak_night_crime_location)

sns.set_style("whitegrid")
sns.set_palette("pastel")
sns.countplot(x="AREA NAME", data=crimes[crimes["NIGHT_CRIME"] == True])
plt.title("Frequency of Night Crimes by Location")
plt.xlabel("Location")
plt.ylabel("Frequency")
plt.xticks(rotation=90)
plt.show()